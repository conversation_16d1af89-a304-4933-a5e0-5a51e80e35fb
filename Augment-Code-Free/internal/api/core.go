package api

import (
	"context"
	"fmt"

	"Augment-Code-Free/internal/handlers"
	"Augment-Code-Free/internal/utils"
)

// App 应用程序结构体
type App struct {
	ctx           context.Context
	currentIDE    *handlers.IDEInfo
	detector      *handlers.IDEDetector
	vscodeHandler *handlers.VSCodeHandler
	jetbrainsHandler *handlers.JetBrainsHandler
}

// NewApp 创建新的应用程序实例
func NewApp() *App {
	return &App{
		detector: handlers.NewIDEDetector(),
	}
}

// OnStartup 应用启动时调用
func (a *App) OnStartup(ctx context.Context) {
	a.ctx = ctx
}

// GetSystemInfo 获取系统信息
func (a *App) GetSystemInfo() map[string]interface{} {
	systemInfo := handlers.GetSystemInfo()
	
	// 添加当前选择的IDE信息
	if a.currentIDE != nil {
		systemInfo["current_ide"] = a.currentIDE
		systemInfo["editor_type"] = a.currentIDE.Name
		systemInfo["ide_type"] = a.currentIDE.Type
		
		// 根据IDE类型添加特定路径信息
		if a.currentIDE.Type == "vscode" {
			paths := utils.GetVSCodePaths(a.getVSCodeConfigName(a.currentIDE.Name))
			systemInfo["vscode_paths"] = paths
		} else if a.currentIDE.Type == "jetbrains" {
			paths := utils.GetJetBrainsPaths()
			systemInfo["jetbrains_paths"] = paths
		}
	}
	
	return map[string]interface{}{
		"success": true,
		"data":    systemInfo,
		"message": "系统信息获取成功",
	}
}

// DetectIDEs 检测已安装的IDE
func (a *App) DetectIDEs() map[string]interface{} {
	return handlers.DetectIDEs()
}

// SetCurrentIDE 设置当前选择的IDE
func (a *App) SetCurrentIDE(ideName string) map[string]interface{} {
	// 先检测所有IDE
	allIDEs := a.detector.DetectAllIDEs()
	
	// 查找指定的IDE
	var selectedIDE *handlers.IDEInfo
	for _, ide := range allIDEs {
		if ide.Name == ideName {
			selectedIDE = &ide
			break
		}
	}
	
	if selectedIDE == nil {
		return map[string]interface{}{
			"success": false,
			"message": fmt.Sprintf("未找到IDE: %s", ideName),
		}
	}
	
	a.currentIDE = selectedIDE
	
	// 根据IDE类型初始化相应的处理器
	if selectedIDE.Type == "vscode" {
		configName := a.getVSCodeConfigName(selectedIDE.Name)
		a.vscodeHandler = handlers.NewVSCodeHandler(configName)
	} else if selectedIDE.Type == "jetbrains" {
		a.jetbrainsHandler = handlers.NewJetBrainsHandler()
	}
	
	return map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"ide":         selectedIDE,
			"editor_type": selectedIDE.Name,
			"ide_type":    selectedIDE.Type,
		},
		"message": fmt.Sprintf("已选择IDE: %s", selectedIDE.DisplayName),
	}
}

// getVSCodeConfigName 获取VSCode系列的配置名称
func (a *App) getVSCodeConfigName(ideName string) string {
	switch ideName {
	case "vscode":
		return "Code"
	case "vscodium":
		return "VSCodium"
	case "cursor":
		return "Cursor"
	default:
		return "VSCodium"
	}
}

// ModifyTelemetryIDs 修改遥测ID
func (a *App) ModifyTelemetryIDs() map[string]interface{} {
	if a.currentIDE == nil {
		return map[string]interface{}{
			"success": false,
			"message": "请先选择一个IDE",
		}
	}
	
	if a.currentIDE.Type == "vscode" {
		if a.vscodeHandler == nil {
			return map[string]interface{}{
				"success": false,
				"message": "VSCode处理器未初始化",
			}
		}
		result := a.vscodeHandler.ModifyTelemetryIDs()
		result["message"] = "VSCode遥测ID修改完成"
		return result
	} else if a.currentIDE.Type == "jetbrains" {
		if a.jetbrainsHandler == nil {
			return map[string]interface{}{
				"success": false,
				"message": "JetBrains处理器未初始化",
			}
		}
		result := a.jetbrainsHandler.ModifyJetBrainsIDs()
		result["message"] = "JetBrains设备ID修改完成"
		return result
	}
	
	return map[string]interface{}{
		"success": false,
		"message": "不支持的IDE类型",
	}
}

// CleanDatabase 清理数据库（仅VSCode系列）
func (a *App) CleanDatabase() map[string]interface{} {
	if a.currentIDE == nil {
		return map[string]interface{}{
			"success": false,
			"message": "请先选择一个IDE",
		}
	}
	
	if a.currentIDE.Type != "vscode" {
		return map[string]interface{}{
			"success": false,
			"message": "数据库清理仅适用于VSCode系列IDE",
		}
	}
	
	if a.vscodeHandler == nil {
		return map[string]interface{}{
			"success": false,
			"message": "VSCode处理器未初始化",
		}
	}
	
	result := a.vscodeHandler.CleanDatabase()
	result["message"] = "数据库清理完成"
	return result
}

// CleanWorkspaceStorage 清理工作区存储（仅VSCode系列）
func (a *App) CleanWorkspaceStorage() map[string]interface{} {
	if a.currentIDE == nil {
		return map[string]interface{}{
			"success": false,
			"message": "请先选择一个IDE",
		}
	}
	
	if a.currentIDE.Type != "vscode" {
		return map[string]interface{}{
			"success": false,
			"message": "工作区清理仅适用于VSCode系列IDE",
		}
	}
	
	if a.vscodeHandler == nil {
		return map[string]interface{}{
			"success": false,
			"message": "VSCode处理器未初始化",
		}
	}
	
	result := a.vscodeHandler.CleanWorkspaceStorage()
	result["message"] = "工作区存储清理完成"
	return result
}

// RunAllOperations 执行所有清理操作
func (a *App) RunAllOperations() map[string]interface{} {
	if a.currentIDE == nil {
		return map[string]interface{}{
			"success": false,
			"message": "请先选择一个IDE",
		}
	}
	
	results := map[string]interface{}{
		"ide_type":        a.currentIDE.Type,
		"overall_success": true,
		"errors":          []string{},
	}
	
	// 修改遥测ID（所有IDE类型都支持）
	telemetryResult := a.ModifyTelemetryIDs()
	results["telemetry"] = telemetryResult
	if !telemetryResult["success"].(bool) {
		results["overall_success"] = false
		if msg, ok := telemetryResult["message"].(string); ok {
			results["errors"] = append(results["errors"].([]string), "遥测ID: "+msg)
		}
	}
	
	if a.currentIDE.Type == "vscode" {
		// VSCode系列：清理数据库和工作区
		dbResult := a.CleanDatabase()
		results["database"] = dbResult
		if !dbResult["success"].(bool) {
			results["overall_success"] = false
			if msg, ok := dbResult["message"].(string); ok {
				results["errors"] = append(results["errors"].([]string), "数据库: "+msg)
			}
		}
		
		wsResult := a.CleanWorkspaceStorage()
		results["workspace"] = wsResult
		if !wsResult["success"].(bool) {
			results["overall_success"] = false
			if msg, ok := wsResult["message"].(string); ok {
				results["errors"] = append(results["errors"].([]string), "工作区: "+msg)
			}
		}
	} else {
		// JetBrains系列：只需要修改遥测ID
		results["database"] = map[string]interface{}{
			"success": true,
			"message": "不适用于JetBrains IDE",
		}
		results["workspace"] = map[string]interface{}{
			"success": true,
			"message": "不适用于JetBrains IDE",
		}
	}
	
	message := "所有操作完成"
	if !results["overall_success"].(bool) {
		message = "部分操作失败"
	}
	
	return map[string]interface{}{
		"success": results["overall_success"],
		"data":    results,
		"message": message,
	}
}

// GetSupportedOperations 获取当前IDE支持的操作
func (a *App) GetSupportedOperations() map[string]interface{} {
	if a.currentIDE == nil {
		return map[string]interface{}{
			"success": false,
			"message": "请先选择一个IDE",
		}
	}
	
	var operations []map[string]interface{}
	
	if a.currentIDE.Type == "jetbrains" {
		operations = []map[string]interface{}{
			{
				"id":          "telemetry",
				"name":        "重置设备ID",
				"description": "重置JetBrains IDE的设备标识符",
				"icon":        "🔑",
				"supported":   true,
			},
		}
	} else {
		operations = []map[string]interface{}{
			{
				"id":          "telemetry",
				"name":        "重置机器码",
				"description": "重置设备 ID 和机器 ID，生成新的随机标识符",
				"icon":        "🔑",
				"supported":   true,
			},
			{
				"id":          "database",
				"name":        "清理数据库",
				"description": "清理 SQLite 数据库中包含 'augment' 的记录",
				"icon":        "🗃️",
				"supported":   true,
			},
			{
				"id":          "workspace",
				"name":        "清理工作区",
				"description": "清理工作区存储文件和目录",
				"icon":        "💾",
				"supported":   true,
			},
		}
	}
	
	return map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"ide_type":   a.currentIDE.Type,
			"operations": operations,
		},
		"message": fmt.Sprintf("获取%s支持的操作列表", a.currentIDE.Type),
	}
}

// GetVersionInfo 获取版本信息
func (a *App) GetVersionInfo() map[string]interface{} {
	return map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"version": "1.0.0",
			"name":    "Augment-Code-Free",
			"author":  "Golang + Wails 版本",
		},
		"message": "版本信息获取成功",
	}
}
