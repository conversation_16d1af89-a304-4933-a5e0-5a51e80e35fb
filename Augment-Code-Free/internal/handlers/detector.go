package handlers

import (
	"fmt"
	"path/filepath"
	"runtime"
	"strings"

	"Augment-Code-Free/internal/utils"
)

// IDEInfo IDE信息结构
type IDEInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Type        string `json:"type"` // "vscode" 或 "jetbrains"
	Path        string `json:"path"`
	Installed   bool   `json:"installed"`
	Version     string `json:"version,omitempty"`
}

// IDEDetector IDE检测器
type IDEDetector struct {
	detectedIDEs []IDEInfo
}

// NewIDEDetector 创建新的IDE检测器
func NewIDEDetector() *IDEDetector {
	return &IDEDetector{
		detectedIDEs: make([]IDEInfo, 0),
	}
}

// DetectAllIDEs 检测所有已安装的IDE
func (d *IDEDetector) DetectAllIDEs() []IDEInfo {
	d.detectedIDEs = make([]IDEInfo, 0)
	
	// 检测 VSCode 系列
	d.detectVSCodeSeries()
	
	// 检测 JetBrains 系列
	d.detectJetBrainsSeries()
	
	return d.detectedIDEs
}

// detectVSCodeSeries 检测 VSCode 系列 IDE
func (d *IDEDetector) detectVSCodeSeries() {
	vscodeIDEs := []struct {
		name        string
		displayName string
		configName  string
	}{
		{"vscode", "Visual Studio Code", "Code"},
		{"vscodium", "VSCodium", "VSCodium"},
		{"cursor", "Cursor", "Cursor"},
	}

	for _, ide := range vscodeIDEs {
		ideInfo := IDEInfo{
			Name:        ide.name,
			DisplayName: ide.displayName,
			Type:        "vscode",
			Installed:   false,
		}

		// 检查配置目录是否存在
		paths := utils.GetVSCodePaths(ide.configName)
		configDir := filepath.Dir(paths["storage_path"])
		
		if utils.DirExists(configDir) {
			ideInfo.Installed = true
			ideInfo.Path = configDir
		}

		d.detectedIDEs = append(d.detectedIDEs, ideInfo)
	}
}

// detectJetBrainsSeries 检测 JetBrains 系列 IDE
func (d *IDEDetector) detectJetBrainsSeries() {
	jetbrainsIDEs := []struct {
		name        string
		displayName string
		configName  string
	}{
		{"idea", "IntelliJ IDEA", "IntelliJIdea"},
		{"pycharm", "PyCharm", "PyCharm"},
		{"goland", "GoLand", "GoLand"},
		{"webstorm", "WebStorm", "WebStorm"},
		{"phpstorm", "PhpStorm", "PhpStorm"},
		{"clion", "CLion", "CLion"},
		{"rider", "Rider", "Rider"},
		{"datagrip", "DataGrip", "DataGrip"},
	}

	jetbrainsConfigDir := utils.GetJetBrainsConfigDir()
	
	for _, ide := range jetbrainsIDEs {
		ideInfo := IDEInfo{
			Name:        ide.name,
			DisplayName: ide.displayName,
			Type:        "jetbrains",
			Installed:   false,
		}

		// 检查 JetBrains 配置目录中是否有对应的IDE配置
		if utils.DirExists(jetbrainsConfigDir) {
			ideConfigPath := d.findJetBrainsIDEConfig(jetbrainsConfigDir, ide.configName)
			if ideConfigPath != "" {
				ideInfo.Installed = true
				ideInfo.Path = ideConfigPath
			}
		}

		d.detectedIDEs = append(d.detectedIDEs, ideInfo)
	}
}

// findJetBrainsIDEConfig 查找 JetBrains IDE 配置目录
func (d *IDEDetector) findJetBrainsIDEConfig(baseDir, ideName string) string {
	// JetBrains IDE 配置目录通常包含版本号，如 IntelliJIdea2023.1
	entries, err := filepath.Glob(filepath.Join(baseDir, ideName+"*"))
	if err != nil {
		return ""
	}

	// 返回最新的版本目录
	for _, entry := range entries {
		if utils.DirExists(entry) {
			return entry
		}
	}

	return ""
}

// GetDefaultIDEs 获取默认的IDE列表（用于未检测到IDE时的显示）
func (d *IDEDetector) GetDefaultIDEs() []IDEInfo {
	return []IDEInfo{
		{
			Name:        "vscodium",
			DisplayName: "VSCodium",
			Type:        "vscode",
			Installed:   false,
		},
		{
			Name:        "vscode",
			DisplayName: "Visual Studio Code",
			Type:        "vscode",
			Installed:   false,
		},
	}
}

// GetInstalledIDEs 获取已安装的IDE列表
func (d *IDEDetector) GetInstalledIDEs() []IDEInfo {
	var installed []IDEInfo
	for _, ide := range d.detectedIDEs {
		if ide.Installed {
			installed = append(installed, ide)
		}
	}
	return installed
}

// GetIDEByName 根据名称获取IDE信息
func (d *IDEDetector) GetIDEByName(name string) *IDEInfo {
	for _, ide := range d.detectedIDEs {
		if strings.EqualFold(ide.Name, name) {
			return &ide
		}
	}
	return nil
}

// GetSystemInfo 获取系统信息
func GetSystemInfo() map[string]interface{} {
	return map[string]interface{}{
		"os":           runtime.GOOS,
		"arch":         runtime.GOARCH,
		"home_dir":     utils.GetHomeDir(),
		"app_data_dir": utils.GetAppDataDir(),
		"jetbrains_config_dir": utils.GetJetBrainsConfigDir(),
	}
}

// DetectIDEs 检测IDE的公共函数
func DetectIDEs() map[string]interface{} {
	detector := NewIDEDetector()
	allIDEs := detector.DetectAllIDEs()
	installedIDEs := detector.GetInstalledIDEs()

	return map[string]interface{}{
		"success":       true,
		"ides":          allIDEs,
		"installed":     installedIDEs,
		"count":         len(allIDEs),
		"installed_count": len(installedIDEs),
		"message":       fmt.Sprintf("检测到 %d 个IDE，其中 %d 个已安装", len(allIDEs), len(installedIDEs)),
	}
}
