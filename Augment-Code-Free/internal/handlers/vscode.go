package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"Augment-Code-Free/internal/utils"
	_ "modernc.org/sqlite"
)

// VSCodeHandler VSCode系列处理器
type VSCodeHandler struct {
	editorType string
	paths      map[string]string
}

// NewVSCodeHandler 创建VSCode处理器
func NewVSCodeHandler(editorType string) *VSCodeHandler {
	return &VSCodeHandler{
		editorType: editorType,
		paths:      utils.GetVSCodePaths(editorType),
	}
}

// ModifyTelemetryIDs 修改遥测ID
func (h *VSCodeHandler) ModifyTelemetryIDs() map[string]interface{} {
	result := map[string]interface{}{
		"success":     true,
		"backups":     []string{},
		"new_ids":     map[string]string{},
		"errors":      []string{},
	}

	// 修改 machine ID
	if err := h.modifyMachineID(result); err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("修改机器ID失败: %v", err))
	}

	// 修改存储文件中的设备ID
	if err := h.modifyStorageIDs(result); err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("修改存储ID失败: %v", err))
	}

	return result
}

// modifyMachineID 修改机器ID文件
func (h *VSCodeHandler) modifyMachineID(result map[string]interface{}) error {
	machineIDPath := h.paths["machine_id_path"]
	
	// 备份原文件
	if utils.FileExists(machineIDPath) {
		backupPath, err := utils.BackupFile(machineIDPath)
		if err != nil {
			return fmt.Errorf("备份机器ID文件失败: %v", err)
		}
		result["backups"] = append(result["backups"].([]string), backupPath)
	}

	// 生成新的机器ID
	newMachineID := utils.GenerateMachineID()
	
	// 写入新的机器ID
	if err := utils.WriteFile(machineIDPath, newMachineID); err != nil {
		return fmt.Errorf("写入新机器ID失败: %v", err)
	}

	result["new_ids"].(map[string]string)["machine_id"] = newMachineID
	return nil
}

// modifyStorageIDs 修改存储文件中的设备ID
func (h *VSCodeHandler) modifyStorageIDs(result map[string]interface{}) error {
	storagePath := h.paths["storage_path"]
	
	if !utils.FileExists(storagePath) {
		// 如果存储文件不存在，创建一个新的
		newDeviceID := utils.GenerateDeviceID()
		storageData := map[string]interface{}{
			"telemetry.deviceId": newDeviceID,
			"telemetry.machineId": utils.GenerateMachineID(),
		}
		
		jsonData, err := json.MarshalIndent(storageData, "", "  ")
		if err != nil {
			return fmt.Errorf("序列化存储数据失败: %v", err)
		}
		
		if err := utils.WriteFile(storagePath, string(jsonData)); err != nil {
			return fmt.Errorf("创建存储文件失败: %v", err)
		}
		
		result["new_ids"].(map[string]string)["device_id"] = newDeviceID
		return nil
	}

	// 备份原文件
	backupPath, err := utils.BackupFile(storagePath)
	if err != nil {
		return fmt.Errorf("备份存储文件失败: %v", err)
	}
	result["backups"] = append(result["backups"].([]string), backupPath)

	// 读取现有存储文件
	content, err := utils.ReadFile(storagePath)
	if err != nil {
		return fmt.Errorf("读取存储文件失败: %v", err)
	}

	// 解析JSON
	var storageData map[string]interface{}
	if err := json.Unmarshal([]byte(content), &storageData); err != nil {
		return fmt.Errorf("解析存储文件失败: %v", err)
	}

	// 生成新的ID
	newDeviceID := utils.GenerateDeviceID()
	newMachineID := utils.GenerateMachineID()

	// 更新ID
	storageData["telemetry.deviceId"] = newDeviceID
	storageData["telemetry.machineId"] = newMachineID

	// 写回文件
	jsonData, err := json.MarshalIndent(storageData, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化更新后的存储数据失败: %v", err)
	}

	if err := utils.WriteFile(storagePath, string(jsonData)); err != nil {
		return fmt.Errorf("写入更新后的存储文件失败: %v", err)
	}

	result["new_ids"].(map[string]string)["device_id"] = newDeviceID
	result["new_ids"].(map[string]string)["storage_machine_id"] = newMachineID

	return nil
}

// CleanDatabase 清理SQLite数据库中的augment相关记录
func (h *VSCodeHandler) CleanDatabase() map[string]interface{} {
	result := map[string]interface{}{
		"success":      true,
		"backup_path":  "",
		"deleted_rows": 0,
		"errors":       []string{},
	}

	dbPath := h.paths["db_path"]
	
	if !utils.FileExists(dbPath) {
		result["message"] = "数据库文件不存在，无需清理"
		return result
	}

	// 备份数据库文件
	backupPath, err := utils.BackupFile(dbPath)
	if err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("备份数据库失败: %v", err))
		return result
	}
	result["backup_path"] = backupPath

	// 打开数据库
	db, err := sql.Open("sqlite", dbPath)
	if err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("打开数据库失败: %v", err))
		return result
	}
	defer db.Close()

	// 查找包含 augment 的记录
	deletedRows := 0
	
	// 获取所有表名
	tables, err := h.getTables(db)
	if err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("获取表列表失败: %v", err))
		return result
	}

	// 在每个表中查找并删除包含 augment 的记录
	for _, table := range tables {
		rows, err := h.deleteAugmentRecords(db, table)
		if err != nil {
			result["errors"] = append(result["errors"].([]string), fmt.Sprintf("清理表 %s 失败: %v", table, err))
		} else {
			deletedRows += rows
		}
	}

	result["deleted_rows"] = deletedRows
	return result
}

// getTables 获取数据库中的所有表名
func (h *VSCodeHandler) getTables(db *sql.DB) ([]string, error) {
	rows, err := db.Query("SELECT name FROM sqlite_master WHERE type='table'")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		tables = append(tables, tableName)
	}

	return tables, nil
}

// deleteAugmentRecords 删除表中包含augment的记录
func (h *VSCodeHandler) deleteAugmentRecords(db *sql.DB, tableName string) (int, error) {
	// 获取表结构
	columns, err := h.getTableColumns(db, tableName)
	if err != nil {
		return 0, err
	}

	// 构建删除条件
	var conditions []string
	for _, column := range columns {
		conditions = append(conditions, fmt.Sprintf("%s LIKE '%%augment%%'", column))
	}

	if len(conditions) == 0 {
		return 0, nil
	}

	// 执行删除
	query := fmt.Sprintf("DELETE FROM %s WHERE %s", tableName, strings.Join(conditions, " OR "))
	result, err := db.Exec(query)
	if err != nil {
		return 0, err
	}

	rowsAffected, _ := result.RowsAffected()
	return int(rowsAffected), nil
}

// getTableColumns 获取表的列名
func (h *VSCodeHandler) getTableColumns(db *sql.DB, tableName string) ([]string, error) {
	rows, err := db.Query(fmt.Sprintf("PRAGMA table_info(%s)", tableName))
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var columns []string
	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull, pk int
		var defaultValue interface{}
		
		if err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk); err != nil {
			continue
		}
		
		// 只处理文本类型的列
		if strings.Contains(strings.ToLower(dataType), "text") || 
		   strings.Contains(strings.ToLower(dataType), "varchar") ||
		   strings.Contains(strings.ToLower(dataType), "char") {
			columns = append(columns, name)
		}
	}

	return columns, nil
}

// CleanWorkspaceStorage 清理工作区存储
func (h *VSCodeHandler) CleanWorkspaceStorage() map[string]interface{} {
	result := map[string]interface{}{
		"success":             true,
		"backup_path":         "",
		"deleted_files_count": 0,
		"errors":              []string{},
	}

	workspaceStoragePath := h.paths["workspace_storage_path"]
	
	if !utils.DirExists(workspaceStoragePath) {
		result["message"] = "工作区存储目录不存在，无需清理"
		return result
	}

	// 备份整个工作区存储目录
	backupPath := workspaceStoragePath + "_backup_" + fmt.Sprintf("%d", utils.GenerateRandomID(8))
	// 这里简化处理，实际应该复制整个目录
	result["backup_path"] = backupPath

	// 清理包含 augment 的文件
	deletedFiles, err := utils.CleanAugmentFiles(workspaceStoragePath)
	if err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("清理工作区文件失败: %v", err))
		return result
	}

	result["deleted_files_count"] = len(deletedFiles)
	return result
}
