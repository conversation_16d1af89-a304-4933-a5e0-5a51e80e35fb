package handlers

import (
	"fmt"
	"path/filepath"

	"Augment-Code-Free/internal/utils"
)

// JetBrainsHandler JetBrains系列处理器
type JetBrainsHandler struct {
	configDir string
	paths     map[string]string
}

// NewJetBrainsHandler 创建JetBrains处理器
func NewJetBrainsHandler() *JetBrainsHandler {
	configDir := utils.GetJetBrainsConfigDir()
	paths := utils.GetJetBrainsPaths()
	
	return &JetBrainsHandler{
		configDir: configDir,
		paths:     paths,
	}
}

// ModifyJetBrainsIDs 修改JetBrains IDE的设备ID
func (h *JetBrainsHandler) ModifyJetBrainsIDs() map[string]interface{} {
	result := map[string]interface{}{
		"success":     true,
		"backups":     []string{},
		"new_ids":     map[string]string{},
		"errors":      []string{},
		"locked_files": []string{},
	}

	if !utils.DirExists(h.configDir) {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), "JetBrains配置目录不存在")
		return result
	}

	// 修改 PermanentDeviceId
	if err := h.modifyPermanentDeviceId(result); err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("修改PermanentDeviceId失败: %v", err))
	}

	// 修改 PermanentUserId
	if err := h.modifyPermanentUserId(result); err != nil {
		result["success"] = false
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("修改PermanentUserId失败: %v", err))
	}

	return result
}

// modifyPermanentDeviceId 修改永久设备ID
func (h *JetBrainsHandler) modifyPermanentDeviceId(result map[string]interface{}) error {
	deviceIdPath := h.paths["permanent_device_id"]
	
	// 如果文件存在，先解锁并备份
	if utils.FileExists(deviceIdPath) {
		// 解锁文件
		if err := utils.UnlockFile(deviceIdPath); err != nil {
			return fmt.Errorf("解锁PermanentDeviceId文件失败: %v", err)
		}
		
		// 备份文件
		backupPath, err := utils.BackupFile(deviceIdPath)
		if err != nil {
			return fmt.Errorf("备份PermanentDeviceId文件失败: %v", err)
		}
		result["backups"] = append(result["backups"].([]string), backupPath)
	}

	// 生成新的设备ID
	newDeviceId := utils.GenerateDeviceID()
	
	// 写入新的设备ID
	if err := utils.WriteFile(deviceIdPath, newDeviceId); err != nil {
		return fmt.Errorf("写入新PermanentDeviceId失败: %v", err)
	}

	// 锁定文件防止重新生成
	if err := utils.LockFile(deviceIdPath); err != nil {
		// 锁定失败不是致命错误，记录警告
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("锁定PermanentDeviceId文件失败: %v", err))
	} else {
		result["locked_files"] = append(result["locked_files"].([]string), deviceIdPath)
	}

	result["new_ids"].(map[string]string)["permanent_device_id"] = newDeviceId
	return nil
}

// modifyPermanentUserId 修改永久用户ID
func (h *JetBrainsHandler) modifyPermanentUserId(result map[string]interface{}) error {
	userIdPath := h.paths["permanent_user_id"]
	
	// 如果文件存在，先解锁并备份
	if utils.FileExists(userIdPath) {
		// 解锁文件
		if err := utils.UnlockFile(userIdPath); err != nil {
			return fmt.Errorf("解锁PermanentUserId文件失败: %v", err)
		}
		
		// 备份文件
		backupPath, err := utils.BackupFile(userIdPath)
		if err != nil {
			return fmt.Errorf("备份PermanentUserId文件失败: %v", err)
		}
		result["backups"] = append(result["backups"].([]string), backupPath)
	}

	// 生成新的用户ID
	newUserId := utils.GenerateDeviceID()
	
	// 写入新的用户ID
	if err := utils.WriteFile(userIdPath, newUserId); err != nil {
		return fmt.Errorf("写入新PermanentUserId失败: %v", err)
	}

	// 锁定文件防止重新生成
	if err := utils.LockFile(userIdPath); err != nil {
		// 锁定失败不是致命错误，记录警告
		result["errors"] = append(result["errors"].([]string), fmt.Sprintf("锁定PermanentUserId文件失败: %v", err))
	} else {
		result["locked_files"] = append(result["locked_files"].([]string), userIdPath)
	}

	result["new_ids"].(map[string]string)["permanent_user_id"] = newUserId
	return nil
}

// GetJetBrainsInfo 获取JetBrains配置信息
func (h *JetBrainsHandler) GetJetBrainsInfo() map[string]interface{} {
	info := map[string]interface{}{
		"config_dir": h.configDir,
		"paths":      h.paths,
		"exists":     utils.DirExists(h.configDir),
	}

	if utils.DirExists(h.configDir) {
		// 检查各个文件的状态
		deviceIdExists := utils.FileExists(h.paths["permanent_device_id"])
		userIdExists := utils.FileExists(h.paths["permanent_user_id"])
		
		info["permanent_device_id_exists"] = deviceIdExists
		info["permanent_user_id_exists"] = userIdExists
		
		// 读取当前ID值（如果存在）
		if deviceIdExists {
			if content, err := utils.ReadFile(h.paths["permanent_device_id"]); err == nil {
				info["current_device_id"] = content
			}
		}
		
		if userIdExists {
			if content, err := utils.ReadFile(h.paths["permanent_user_id"]); err == nil {
				info["current_user_id"] = content
			}
		}

		// 检查已安装的JetBrains IDE
		installedIDEs := h.findInstalledJetBrainsIDEs()
		info["installed_ides"] = installedIDEs
	}

	return info
}

// findInstalledJetBrainsIDEs 查找已安装的JetBrains IDE
func (h *JetBrainsHandler) findInstalledJetBrainsIDEs() []string {
	var installedIDEs []string
	
	if !utils.DirExists(h.configDir) {
		return installedIDEs
	}

	// 常见的JetBrains IDE配置目录前缀
	ideNames := []string{
		"IntelliJIdea",
		"PyCharm",
		"GoLand",
		"WebStorm",
		"PhpStorm",
		"CLion",
		"Rider",
		"DataGrip",
		"AndroidStudio",
	}

	for _, ideName := range ideNames {
		// 查找匹配的配置目录（可能包含版本号）
		pattern := filepath.Join(h.configDir, ideName+"*")
		matches, err := filepath.Glob(pattern)
		if err != nil {
			continue
		}

		for _, match := range matches {
			if utils.DirExists(match) {
				installedIDEs = append(installedIDEs, filepath.Base(match))
			}
		}
	}

	return installedIDEs
}

// UnlockAllFiles 解锁所有JetBrains相关文件
func (h *JetBrainsHandler) UnlockAllFiles() map[string]interface{} {
	result := map[string]interface{}{
		"success":        true,
		"unlocked_files": []string{},
		"errors":         []string{},
	}

	files := []string{
		h.paths["permanent_device_id"],
		h.paths["permanent_user_id"],
	}

	for _, filePath := range files {
		if utils.FileExists(filePath) {
			if err := utils.UnlockFile(filePath); err != nil {
				result["errors"] = append(result["errors"].([]string), fmt.Sprintf("解锁文件 %s 失败: %v", filePath, err))
			} else {
				result["unlocked_files"] = append(result["unlocked_files"].([]string), filePath)
			}
		}
	}

	if len(result["errors"].([]string)) > 0 {
		result["success"] = false
	}

	return result
}

// LockAllFiles 锁定所有JetBrains相关文件
func (h *JetBrainsHandler) LockAllFiles() map[string]interface{} {
	result := map[string]interface{}{
		"success":      true,
		"locked_files": []string{},
		"errors":       []string{},
	}

	files := []string{
		h.paths["permanent_device_id"],
		h.paths["permanent_user_id"],
	}

	for _, filePath := range files {
		if utils.FileExists(filePath) {
			if err := utils.LockFile(filePath); err != nil {
				result["errors"] = append(result["errors"].([]string), fmt.Sprintf("锁定文件 %s 失败: %v", filePath, err))
			} else {
				result["locked_files"] = append(result["locked_files"].([]string), filePath)
			}
		}
	}

	if len(result["errors"].([]string)) > 0 {
		result["success"] = false
	}

	return result
}
