package utils

import (
	"os"
	"path/filepath"
	"runtime"
)

// GetHomeDir 获取用户主目录
func GetHomeDir() string {
	home, err := os.UserHomeDir()
	if err != nil {
		return ""
	}
	return home
}

// GetAppDataDir 获取应用数据目录
func GetAppDataDir() string {
	switch runtime.GOOS {
	case "windows":
		return os.Getenv("APPDATA")
	case "darwin":
		home := GetHomeDir()
		return filepath.Join(home, "Library", "Application Support")
	default: // linux
		home := GetHomeDir()
		return filepath.Join(home, ".config")
	}
}

// GetVSCodePaths 获取 VSCode 系列的路径信息
func GetVSCodePaths(editorType string) map[string]string {
	appDataDir := GetAppDataDir()
	homeDir := GetHomeDir()
	
	var basePath string
	switch editorType {
	case "Code":
		basePath = filepath.Join(appDataDir, "Code")
	case "VSCodium":
		basePath = filepath.Join(appDataDir, "VSCodium")
	case "Cursor":
		basePath = filepath.Join(appDataDir, "Cursor")
	default:
		basePath = filepath.Join(appDataDir, "VSCodium")
	}

	paths := map[string]string{
		"storage_path":           filepath.Join(basePath, "User", "globalStorage", "storage.json"),
		"machine_id_path":        filepath.Join(basePath, "machineid"),
		"workspace_storage_path": filepath.Join(basePath, "User", "workspaceStorage"),
	}

	// SQLite 数据库路径
	switch runtime.GOOS {
	case "windows":
		paths["db_path"] = filepath.Join(basePath, "User", "globalStorage", "state.vscdb")
	case "darwin":
		paths["db_path"] = filepath.Join(homeDir, "Library", "Application Support", editorType, "User", "globalStorage", "state.vscdb")
	default: // linux
		paths["db_path"] = filepath.Join(homeDir, ".config", editorType, "User", "globalStorage", "state.vscdb")
	}

	return paths
}

// GetJetBrainsConfigDir 获取 JetBrains 配置目录
func GetJetBrainsConfigDir() string {
	homeDir := GetHomeDir()
	
	switch runtime.GOOS {
	case "windows":
		appData := os.Getenv("APPDATA")
		return filepath.Join(appData, "JetBrains")
	case "darwin":
		return filepath.Join(homeDir, "Library", "Application Support", "JetBrains")
	default: // linux
		return filepath.Join(homeDir, ".config", "JetBrains")
	}
}

// GetJetBrainsPaths 获取 JetBrains IDE 的路径信息
func GetJetBrainsPaths() map[string]string {
	configDir := GetJetBrainsConfigDir()
	
	return map[string]string{
		"config_dir":              configDir,
		"permanent_device_id":     filepath.Join(configDir, "PermanentDeviceId"),
		"permanent_user_id":       filepath.Join(configDir, "PermanentUserId"),
	}
}

// EnsureDir 确保目录存在
func EnsureDir(path string) error {
	return os.MkdirAll(path, 0755)
}

// FileExists 检查文件是否存在
func FileExists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// DirExists 检查目录是否存在
func DirExists(path string) bool {
	info, err := os.Stat(path)
	if os.IsNotExist(err) {
		return false
	}
	return info.IsDir()
}
