package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// GenerateRandomID 生成随机ID
func GenerateRandomID(length int) string {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选
		return fmt.Sprintf("%x", time.Now().UnixNano())[:length]
	}
	return hex.EncodeToString(bytes)
}

// GenerateMachineID 生成机器ID格式的随机字符串
func GenerateMachineID() string {
	// 生成类似 "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0" 的格式
	return GenerateRandomID(64)
}

// GenerateDeviceID 生成设备ID格式的随机字符串
func GenerateDeviceID() string {
	// 生成类似 UUID 的格式
	id := GenerateRandomID(32)
	return fmt.Sprintf("%s-%s-%s-%s-%s",
		id[0:8], id[8:12], id[12:16], id[16:20], id[20:32])
}

// BackupFile 备份文件
func BackupFile(filePath string) (string, error) {
	if !FileExists(filePath) {
		return "", fmt.Errorf("文件不存在: %s", filePath)
	}

	// 创建备份文件名
	timestamp := time.Now().Format("20060102_150405")
	backupPath := fmt.Sprintf("%s.backup_%s", filePath, timestamp)

	// 复制文件
	source, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("无法打开源文件: %v", err)
	}
	defer source.Close()

	destination, err := os.Create(backupPath)
	if err != nil {
		return "", fmt.Errorf("无法创建备份文件: %v", err)
	}
	defer destination.Close()

	_, err = io.Copy(destination, source)
	if err != nil {
		return "", fmt.Errorf("备份文件失败: %v", err)
	}

	return backupPath, nil
}

// WriteFile 写入文件内容
func WriteFile(filePath, content string) error {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := EnsureDir(dir); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	return os.WriteFile(filePath, []byte(content), 0644)
}

// ReadFile 读取文件内容
func ReadFile(filePath string) (string, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return string(content), nil
}

// DeleteFile 删除文件
func DeleteFile(filePath string) error {
	if !FileExists(filePath) {
		return nil // 文件不存在，认为删除成功
	}
	return os.Remove(filePath)
}

// DeleteDirectory 删除目录及其内容
func DeleteDirectory(dirPath string) error {
	if !DirExists(dirPath) {
		return nil // 目录不存在，认为删除成功
	}
	return os.RemoveAll(dirPath)
}

// ListFiles 列出目录中的文件
func ListFiles(dirPath string) ([]string, error) {
	if !DirExists(dirPath) {
		return []string{}, nil
	}

	var files []string
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			files = append(files, path)
		}
		return nil
	})

	return files, err
}

// CountFiles 统计目录中的文件数量
func CountFiles(dirPath string) (int, error) {
	files, err := ListFiles(dirPath)
	return len(files), err
}

// LockFile 锁定文件（设置为只读）
func LockFile(filePath string) error {
	if !FileExists(filePath) {
		return fmt.Errorf("文件不存在: %s", filePath)
	}
	return os.Chmod(filePath, 0444) // 只读权限
}

// UnlockFile 解锁文件（恢复读写权限）
func UnlockFile(filePath string) error {
	if !FileExists(filePath) {
		return fmt.Errorf("文件不存在: %s", filePath)
	}
	return os.Chmod(filePath, 0644) // 读写权限
}

// CleanAugmentFiles 清理包含 augment 关键字的文件
func CleanAugmentFiles(dirPath string) ([]string, error) {
	if !DirExists(dirPath) {
		return []string{}, nil
	}

	var deletedFiles []string
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 检查文件名是否包含 augment（不区分大小写）
		if !info.IsDir() && strings.Contains(strings.ToLower(info.Name()), "augment") {
			if err := os.Remove(path); err == nil {
				deletedFiles = append(deletedFiles, path)
			}
		}

		return nil
	})

	return deletedFiles, err
}
