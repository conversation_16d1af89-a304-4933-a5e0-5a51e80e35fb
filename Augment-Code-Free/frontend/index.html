<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>Augment-Code-Free</title>
    <link rel="stylesheet" href="./src/style.css">
</head>
<body>
    <div id="app">
        <!-- 头部 -->
        <header class="header">
            <h1>⚙ Augment-Code-Free</h1>
            <p>重置 Augment插件数据的简易工具</p>
            <div class="status">
                <span id="status-text">状态: 检查中...</span>
            </div>
        </header>

        <!-- IDE 选择区域 -->
        <section class="ide-selection">
            <h2>🎯 选择编辑器</h2>
            <div class="ide-buttons" id="ide-buttons">
                <!-- IDE 按钮将通过 JavaScript 动态生成 -->
            </div>
            <button id="detect-btn" class="detect-btn">🔍 重新检测</button>
        </section>

        <!-- 系统信息 -->
        <section class="system-info">
            <h2>📁 系统信息</h2>
            <button id="refresh-info-btn" class="refresh-btn">🔄</button>
            <div id="system-info-content" class="info-content">
                正在加载系统信息...
            </div>
        </section>

        <!-- 操作区域 -->
        <section class="operations">
            <h2>🛠️ 清理操作</h2>
            <div id="operations-list" class="operations-list">
                <!-- 操作按钮将通过 JavaScript 动态生成 -->
            </div>
        </section>

        <!-- 结果显示 -->
        <section class="results">
            <h2>📊 操作结果</h2>
            <button id="clear-results-btn" class="clear-btn">✕</button>
            <div id="results-content" class="results-content">
                <!-- 结果将在这里显示 -->
            </div>
        </section>

        <!-- 警告信息 -->
        <section class="warnings">
            <div class="warning-box">
                <p>⚠️ 使用前请确保已退出 IDE 和 AugmentCode 插件</p>
                <p>💡 操作完成后重启 IDE 并使用新邮箱登录 AugmentCode</p>
            </div>
        </section>

        <!-- 关于对话框 -->
        <div id="about-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>🐱 关于 Augment-Code-Free</h2>
                <div class="about-content">
                    <h3>📋 项目信息</h3>
                    <p><strong>版本:</strong> <span id="app-version">v1.0.0</span></p>
                    <p><strong>作者:</strong> Golang + Wails 版本</p>
                    <p><strong>描述:</strong> 用于清理 AugmentCode 相关数据的现代化 GUI 工具</p>

                    <h3>⚠️ 免责声明</h3>
                    <p><strong>使用风险自负：</strong>本工具仅供学习和研究目的使用，使用者需自行承担使用风险。</p>
                    <p><strong>数据安全：</strong>使用前请确保重要数据已备份，作者不对任何数据丢失负责。</p>
                    <p><strong>合规使用：</strong>请遵守相关软件的使用条款和当地法律法规。</p>
                </div>
            </div>
        </div>

        <!-- 加载遮罩 -->
        <div id="loading-overlay" class="loading-overlay">
            <div class="loading-spinner"></div>
            <p>正在处理...</p>
        </div>
    </div>

    <script src="./src/main.js" type="module"></script>
</body>
</html>
