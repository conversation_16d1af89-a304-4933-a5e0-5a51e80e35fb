import './style.css';

// 导入 Wails 生成的 Go 函数
import {
    DetectIDEs,
    SetCurrentIDE,
    GetSystemInfo,
    ModifyTelemetryIDs,
    CleanDatabase,
    CleanWorkspaceStorage,
    RunAllOperations,
    GetSupportedOperations,
    GetVersionInfo
} from '../wailsjs/go/main/App';

// 全局状态
let currentIDE = null;
let supportedOperations = [];

// DOM 元素
const statusText = document.getElementById('status-text');
const ideButtons = document.getElementById('ide-buttons');
const detectBtn = document.getElementById('detect-btn');
const refreshInfoBtn = document.getElementById('refresh-info-btn');
const systemInfoContent = document.getElementById('system-info-content');
const operationsList = document.getElementById('operations-list');
const resultsContent = document.getElementById('results-content');
const clearResultsBtn = document.getElementById('clear-results-btn');
const aboutModal = document.getElementById('about-modal');
const loadingOverlay = document.getElementById('loading-overlay');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

// 初始化应用
async function initializeApp() {
    try {
        updateStatus('正在初始化...', 'info');

        // 获取版本信息
        const versionInfo = await GetVersionInfo();
        if (versionInfo.success) {
            document.getElementById('app-version').textContent = versionInfo.data.version;
        }

        // 检测IDE
        await detectIDEs();

        // 获取系统信息
        await refreshSystemInfo();

        updateStatus('就绪', 'success');
    } catch (error) {
        console.error('初始化失败:', error);
        updateStatus('初始化失败', 'error');
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 检测IDE按钮
    detectBtn.addEventListener('click', detectIDEs);

    // 刷新系统信息按钮
    refreshInfoBtn.addEventListener('click', refreshSystemInfo);

    // 清除结果按钮
    clearResultsBtn.addEventListener('click', clearResults);

    // 关于模态框
    const aboutBtn = document.querySelector('.about-btn');
    const closeBtn = document.querySelector('.close');

    if (aboutBtn) {
        aboutBtn.addEventListener('click', () => {
            aboutModal.style.display = 'block';
        });
    }

    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            aboutModal.style.display = 'none';
        });
    }

    // 点击模态框外部关闭
    window.addEventListener('click', (event) => {
        if (event.target === aboutModal) {
            aboutModal.style.display = 'none';
        }
    });
}

// 检测IDE
async function detectIDEs() {
    try {
        showLoading('正在检测IDE...');

        const result = await DetectIDEs();

        if (result.success) {
            renderIDEButtons(result.ides);
            updateStatus(`检测到 ${result.installed_count} 个已安装的IDE`, 'success');
        } else {
            updateStatus('IDE检测失败', 'error');
            addResult('IDE检测失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('检测IDE失败:', error);
        updateStatus('IDE检测失败', 'error');
        addResult('IDE检测失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 渲染IDE按钮
function renderIDEButtons(ides) {
    ideButtons.innerHTML = '';

    ides.forEach(ide => {
        const button = document.createElement('button');
        button.className = `ide-btn ${!ide.installed ? 'not-installed' : ''}`;
        button.innerHTML = `
            ${getIDEIcon(ide.name)} ${ide.display_name}
            ${!ide.installed ? ' (未安装)' : ''}
        `;

        if (ide.installed) {
            button.addEventListener('click', () => selectIDE(ide));
        }

        ideButtons.appendChild(button);
    });
}

// 获取IDE图标
function getIDEIcon(ideName) {
    const icons = {
        'vscode': '💙',
        'vscodium': '🔷',
        'cursor': '🎯',
        'idea': '🧠',
        'pycharm': '🐍',
        'goland': '🐹',
        'webstorm': '🌐',
        'phpstorm': '🐘',
        'clion': '⚡',
        'rider': '🏃',
        'datagrip': '🗃️'
    };
    return icons[ideName] || '📝';
}

// 选择IDE
async function selectIDE(ide) {
    try {
        showLoading('正在设置IDE...');

        const result = await SetCurrentIDE(ide.name);

        if (result.success) {
            currentIDE = ide;

            // 更新按钮状态
            document.querySelectorAll('.ide-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            event.target.classList.add('selected');

            // 获取支持的操作
            await loadSupportedOperations();

            // 刷新系统信息
            await refreshSystemInfo();

            updateStatus(`已选择: ${ide.display_name}`, 'success');
            addResult(`已选择IDE: ${ide.display_name}`, 'success');
        } else {
            addResult('设置IDE失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('选择IDE失败:', error);
        addResult('选择IDE失败: ' + error.message, 'error');
    } finally {
        hideLoading();
    }
}

// 加载支持的操作
async function loadSupportedOperations() {
    try {
        const result = await GetSupportedOperations();

        if (result.success) {
            supportedOperations = result.data.operations;
            renderOperations();
        }
    } catch (error) {
        console.error('获取支持的操作失败:', error);
    }
}

// 渲染操作按钮
function renderOperations() {
    operationsList.innerHTML = '';

    // 添加单个操作
    supportedOperations.forEach(operation => {
        const card = document.createElement('div');
        card.className = 'operation-card';
        card.innerHTML = `
            <h3>${operation.icon} ${operation.name}</h3>
            <p>${operation.description}</p>
            <button class="operation-btn" onclick="executeOperation('${operation.id}')">
                ${operation.name}
            </button>
        `;
        operationsList.appendChild(card);
    });

    // 添加一键清理按钮
    const runAllCard = document.createElement('div');
    runAllCard.className = 'operation-card';
    runAllCard.innerHTML = `
        <h3>🚀 一键清理</h3>
        <p>执行所有清理操作（推荐）</p>
        <button class="operation-btn run-all-btn" onclick="executeAllOperations()">
            立即执行
        </button>
    `;
    operationsList.appendChild(runAllCard);
}

// 执行单个操作
async function executeOperation(operationId) {
    if (!currentIDE) {
        addResult('请先选择一个IDE', 'error');
        return;
    }

    try {
        showLoading('正在执行操作...');

        let result;
        switch (operationId) {
            case 'telemetry':
                result = await ModifyTelemetryIDs();
                break;
            case 'database':
                result = await CleanDatabase();
                break;
            case 'workspace':
                result = await CleanWorkspaceStorage();
                break;
            default:
                throw new Error('未知的操作类型');
        }

        if (result.success) {
            addResult(`${getOperationName(operationId)}成功`, 'success');
            if (result.data) {
                addResult(JSON.stringify(result.data, null, 2), 'info');
            }
        } else {
            addResult(`${getOperationName(operationId)}失败: ${result.message}`, 'error');
            if (result.errors && result.errors.length > 0) {
                result.errors.forEach(error => {
                    addResult(`错误: ${error}`, 'error');
                });
            }
        }
    } catch (error) {
        console.error('执行操作失败:', error);
        addResult(`执行操作失败: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

// 执行所有操作
async function executeAllOperations() {
    if (!currentIDE) {
        addResult('请先选择一个IDE', 'error');
        return;
    }

    try {
        showLoading('正在执行所有操作...');

        const result = await RunAllOperations();

        if (result.success) {
            addResult('所有操作执行完成', 'success');

            // 显示详细结果
            if (result.data) {
                const data = result.data;

                // 遥测ID结果
                if (data.telemetry) {
                    const status = data.telemetry.success ? 'success' : 'error';
                    addResult(`遥测ID修改: ${data.telemetry.success ? '成功' : '失败'}`, status);
                }

                // 数据库清理结果
                if (data.database) {
                    const status = data.database.success ? 'success' : 'error';
                    addResult(`数据库清理: ${data.database.success ? '成功' : '失败'}`, status);
                }

                // 工作区清理结果
                if (data.workspace) {
                    const status = data.workspace.success ? 'success' : 'error';
                    addResult(`工作区清理: ${data.workspace.success ? '成功' : '失败'}`, status);
                }

                // 显示错误信息
                if (data.errors && data.errors.length > 0) {
                    data.errors.forEach(error => {
                        addResult(`错误: ${error}`, 'error');
                    });
                }
            }
        } else {
            addResult(`操作失败: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('执行所有操作失败:', error);
        addResult(`执行所有操作失败: ${error.message}`, 'error');
    } finally {
        hideLoading();
    }
}

// 获取操作名称
function getOperationName(operationId) {
    const names = {
        'telemetry': '遥测ID修改',
        'database': '数据库清理',
        'workspace': '工作区清理'
    };
    return names[operationId] || operationId;
}

// 刷新系统信息
async function refreshSystemInfo() {
    try {
        const result = await GetSystemInfo();

        if (result.success) {
            displaySystemInfo(result.data);
        } else {
            systemInfoContent.textContent = '获取系统信息失败: ' + result.message;
        }
    } catch (error) {
        console.error('获取系统信息失败:', error);
        systemInfoContent.textContent = '获取系统信息失败: ' + error.message;
    }
}

// 显示系统信息
function displaySystemInfo(data) {
    let info = '';

    info += `操作系统: ${data.os}\n`;
    info += `架构: ${data.arch}\n`;
    info += `用户目录: ${data.home_dir}\n`;
    info += `应用数据目录: ${data.app_data_dir}\n`;

    if (data.current_ide) {
        info += `\n当前IDE: ${data.current_ide.display_name}\n`;
        info += `IDE类型: ${data.current_ide.type}\n`;
        info += `安装状态: ${data.current_ide.installed ? '已安装' : '未安装'}\n`;

        if (data.vscode_paths) {
            info += '\nVSCode路径:\n';
            Object.entries(data.vscode_paths).forEach(([key, value]) => {
                info += `  ${key}: ${value}\n`;
            });
        }

        if (data.jetbrains_paths) {
            info += '\nJetBrains路径:\n';
            Object.entries(data.jetbrains_paths).forEach(([key, value]) => {
                info += `  ${key}: ${value}\n`;
            });
        }
    }

    systemInfoContent.textContent = info;
}

// 添加结果
function addResult(message, type = 'info') {
    const resultItem = document.createElement('div');
    resultItem.className = `result-item ${type}`;

    const timestamp = new Date().toLocaleTimeString();
    resultItem.innerHTML = `
        <strong>[${timestamp}]</strong> ${message}
    `;

    resultsContent.appendChild(resultItem);
    resultsContent.scrollTop = resultsContent.scrollHeight;
}

// 清除结果
function clearResults() {
    resultsContent.innerHTML = '';
}

// 更新状态
function updateStatus(message, type = 'info') {
    statusText.textContent = `状态: ${message}`;

    // 更新状态样式
    const statusElement = document.querySelector('.status');
    statusElement.className = 'status';

    if (type === 'success') {
        statusElement.style.background = '#e8f5e8';
        statusElement.style.borderColor = '#4caf50';
        statusElement.style.color = '#2e7d32';
    } else if (type === 'error') {
        statusElement.style.background = '#ffebee';
        statusElement.style.borderColor = '#f44336';
        statusElement.style.color = '#c62828';
    } else if (type === 'warning') {
        statusElement.style.background = '#fff3e0';
        statusElement.style.borderColor = '#ff9800';
        statusElement.style.color = '#ef6c00';
    } else {
        statusElement.style.background = '#e3f2fd';
        statusElement.style.borderColor = '#2196f3';
        statusElement.style.color = '#1565c0';
    }
}

// 显示加载遮罩
function showLoading(message = '正在处理...') {
    loadingOverlay.querySelector('p').textContent = message;
    loadingOverlay.style.display = 'flex';
}

// 隐藏加载遮罩
function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// 全局函数，供HTML调用
window.executeOperation = executeOperation;
window.executeAllOperations = executeAllOperations;
