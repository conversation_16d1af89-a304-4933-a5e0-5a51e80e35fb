/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

#app {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 20px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 15px;
}

.status {
    display: inline-block;
    padding: 8px 16px;
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 20px;
    color: #2e7d32;
    font-weight: 500;
}

/* 区块样式 */
section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

section h2 {
    font-size: 1.5em;
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* IDE 选择样式 */
.ide-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    justify-content: center;
}

.ide-btn {
    padding: 15px 25px;
    border: 2px solid #ddd;
    border-radius: 10px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1em;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.ide-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.ide-btn.selected {
    border-color: #667eea;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.ide-btn.not-installed {
    opacity: 0.5;
    cursor: not-allowed;
}

.detect-btn, .refresh-btn, .clear-btn {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.detect-btn:hover, .refresh-btn:hover, .clear-btn:hover {
    background: #f5f5f5;
    border-color: #667eea;
}

/* 系统信息样式 */
.info-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: left;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
}

/* 操作按钮样式 */
.operations-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.operation-card {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    background: white;
    transition: all 0.3s ease;
}

.operation-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.operation-card h3 {
    font-size: 1.2em;
    margin-bottom: 10px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.operation-card p {
    color: #666;
    margin-bottom: 15px;
    font-size: 0.9em;
}

.operation-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    transition: all 0.3s ease;
}

.operation-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.operation-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 一键清理按钮 */
.run-all-btn {
    grid-column: 1 / -1;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    font-size: 1.1em;
    padding: 15px;
}

.run-all-btn:hover {
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* 结果显示样式 */
.results-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    text-align: left;
    max-height: 400px;
    overflow-y: auto;
}

.result-item {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 6px;
    border-left: 4px solid #ddd;
}

.result-item.success {
    background: #e8f5e8;
    border-left-color: #4caf50;
}

.result-item.error {
    background: #ffebee;
    border-left-color: #f44336;
}

.result-item.warning {
    background: #fff3e0;
    border-left-color: #ff9800;
}

/* 警告信息样式 */
.warnings {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid #ffc107;
}

.warning-box p {
    margin-bottom: 10px;
    color: #856404;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

.about-content h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #333;
}

.about-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}

/* 加载遮罩样式 */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    color: white;
    font-size: 1.2em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #app {
        padding: 10px;
    }

    .header h1 {
        font-size: 2em;
    }

    .ide-buttons {
        flex-direction: column;
        align-items: center;
    }

    .ide-btn {
        width: 100%;
        max-width: 300px;
    }

    .operations-list {
        grid-template-columns: 1fr;
    }

    section {
        padding: 15px;
    }
}
