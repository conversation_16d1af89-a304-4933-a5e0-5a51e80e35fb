<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steam账号管理工具 - 使用说明</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #667eea;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
            display: flex;
            align-items: center;
        }

        .section h2::before {
            content: "🔧";
            margin-right: 10px;
            font-size: 1.2em;
        }

        .section h3 {
            color: #495057;
            font-size: 1.3em;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        .section h3::before {
            content: "▶";
            color: #667eea;
            margin-right: 8px;
            font-size: 0.8em;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease;
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .feature-card h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }

        .step-list li {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            position: relative;
            padding-left: 60px;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: #28a745;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9em;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #f39c12;
        }

        .warning::before {
            content: "⚠️ ";
            font-weight: bold;
            color: #f39c12;
        }

        .tip {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }

        .tip::before {
            content: "💡 ";
            font-weight: bold;
            color: #17a2b8;
        }

        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
            overflow-x: auto;
        }

        .button-demo {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.9em;
            margin: 2px;
            border: none;
            cursor: pointer;
        }

        .button-demo.success { background: #28a745; }
        .button-demo.warning { background: #ffc107; color: #212529; }
        .button-demo.danger { background: #dc3545; }
        .button-demo.info { background: #17a2b8; }

        .status-demo {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            margin: 2px;
        }

        .status-demo.normal { background: #c6f6d5; color: #22543d; }
        .status-demo.banned { background: #fed7d7; color: #742a2a; }
        .status-demo.unknown { background: #e2e8f0; color: #4a5568; }

        .steamid-demo {
            background: #ebf8ff;
            color: #3182ce;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            cursor: pointer;
            border: 1px solid #90cdf4;
        }

        .toc {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .toc h3 {
            margin-bottom: 15px;
            color: #667eea;
        }

        .toc ul {
            list-style: none;
            padding: 0;
        }

        .toc li {
            margin-bottom: 8px;
        }

        .toc a {
            color: #495057;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .toc a:hover {
            background: #e9ecef;
            color: #667eea;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .step-list li {
                padding-left: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Steam账号管理工具</h1>
            <p>专业的Steam多账号管理解决方案 v1.0</p>
        </div>

        <div class="content">
            <!-- 目录 -->
            <div class="toc">
                <h3>📋 目录</h3>
                <ul>
                    <li><a href="#overview">功能概述</a></li>
                    <li><a href="#installation">安装与启动</a></li>
                    <li><a href="#account-management">账号管理</a></li>
                    <li><a href="#steam-features">Steam功能</a></li>
                    <li><a href="#game-features">游戏功能</a></li>
                    <li><a href="#annie-features">安妮程序</a></li>
                    <li><a href="#advanced">高级功能</a></li>
                    <li><a href="#troubleshooting">常见问题</a></li>
                </ul>
            </div>

            <!-- 功能概述 -->
            <div class="section" id="overview">
                <h2>功能概述</h2>
                <p>Steam账号管理工具是一个专业的多账号管理解决方案，帮助您高效管理多个Steam账号，支持自动登录、游戏启动、封禁检测等功能。</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🔐 账号管理</h4>
                        <p>安全存储多个Steam账号信息，支持批量导入、编辑和删除操作。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🚀 自动登录</h4>
                        <p>一键登录Steam账号，自动处理Steam客户端启动和账号切换。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🎮 游戏启动</h4>
                        <p>支持自动启动指定游戏，智能关闭现有游戏进程。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🛡️封禁检测</h4>
                        <p>自动检测账号VAC封禁和游戏封禁状态，实时更新状态信息。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔗 Steam集成</h4>
                        <p>自动获取SteamID，支持点击打开Steam个人页面。</p>
                    </div>
                    <div class="feature-card">
                        <h4>⚙️ 智能配置</h4>
                        <p>丰富的配置选项，支持自定义游戏、安妮程序等功能。</p>
                    </div>
                </div>
            </div>

            <!-- 安装与启动 -->
            <div class="section" id="installation">
                <h2>安装与启动</h2>
                
                <h3>系统要求</h3>
                <ul>
                    <li>Windows 10/11 操作系统</li>
                    <li>已安装Steam客户端</li>
                    <li>网络连接（用于Steam API调用）</li>
                </ul>

                <h3>启动步骤</h3>
                <ol class="step-list">
                    <li>下载并解压程序文件到任意目录</li>
                    <li>双击运行 <code>steam-manager-simple.exe</code></li>
                    <li>程序会自动在默认浏览器中打开管理界面</li>
                    <li>首次使用建议先进行配置设置</li>
                </ol>

                <div class="tip">
                    程序启动后会在系统托盘显示图标，关闭浏览器窗口不会退出程序。
                </div>
            </div>

            <!-- 账号管理 -->
            <div class="section" id="account-management">
                <h2>账号管理</h2>

                <h3>添加账号</h3>
                <ol class="step-list">
                    <li>点击界面上方的 <span class="button-demo">添加账号</span> 按钮</li>
                    <li>填写Steam用户名和密码</li>
                    <li>可选填写备注信息（如账号用途、游戏等）</li>
                    <li>点击 <span class="button-demo success">添加</span> 保存账号</li>
                    <li>程序会自动在后台获取该账号的SteamID</li>
                </ol>

                <h3>批量导入账号</h3>
                <ol class="step-list">
                    <li>点击 <span class="button-demo">批量导入</span> 按钮</li>
                    <li>按照格式输入账号信息：<br>
                        <div class="code">用户名:密码:备注<br>用户名:密码:备注</div>
                    </li>
                    <li>每行一个账号，用冒号分隔</li>
                    <li>点击 <span class="button-demo success">导入</span> 完成批量添加</li>
                </ol>

                <h3>账号状态说明</h3>
                <p>账号列表中会显示以下状态信息：</p>
                <ul>
                    <li><span class="status-demo normal">正常</span> - 账号无封禁记录</li>
                    <li><span class="status-demo banned">VAC封禁</span> - 账号被VAC系统封禁</li>
                    <li><span class="status-demo banned">游戏封禁</span> - 账号有游戏封禁记录</li>
                    <li><span class="status-demo unknown">未知</span> - 尚未检测或检测失败</li>
                </ul>

                <h3>SteamID功能</h3>
                <p>程序会自动获取每个账号的SteamID，显示为可点击的蓝色链接：</p>
                <p><span class="steamid-demo">76561199214036490</span></p>
                <ul>
                    <li>点击SteamID可直接在浏览器中打开Steam个人页面</li>
                    <li>如果显示"获取失败"，可点击重试按钮重新获取</li>
                    <li>也可以手动设置SteamID（点击编辑按钮）</li>
                </ul>
            </div>

            <!-- Steam功能 -->
            <div class="section" id="steam-features">
                <h2>Steam功能</h2>

                <h3>账号登录</h3>
                <ol class="step-list">
                    <li>在账号列表中找到要登录的账号</li>
                    <li>点击该账号行的 <span class="button-demo">登录</span> 按钮</li>
                    <li>程序会自动关闭现有Steam进程</li>
                    <li>启动Steam客户端并自动登录指定账号</li>
                    <li>登录成功后账号会显示"当前登录"标识</li>
                </ol>

                <div class="warning">
                    登录过程中请不要手动操作Steam客户端，等待自动登录完成。
                </div>

                <h3>封禁状态检测</h3>
                <ol class="step-list">
                    <li>确保账号已获取到有效的SteamID</li>
                    <li>点击账号行的 <span class="button-demo warning">检查</span> 按钮</li>
                    <li>程序会调用Steam API检测封禁状态</li>
                    <li>检测结果会自动更新到账号状态列</li>
                </ol>

                <div class="tip">
                    程序会在启动时自动检测1小时内未检测的账号封禁状态。
                </div>
            </div>

            <!-- 游戏功能 -->
            <div class="section" id="game-features">
                <h2>游戏功能</h2>

                <h3>游戏配置</h3>
                <ol class="step-list">
                    <li>点击右上角的 <span class="button-demo">配置</span> 按钮</li>
                    <li>在游戏设置区域进行配置：
                        <ul>
                            <li><strong>自动启动游戏</strong>：登录成功后自动启动选定的游戏</li>
                            <li><strong>自动关闭游戏</strong>：登录前自动关闭现有游戏进程</li>
                            <li><strong>选择游戏</strong>：从已安装的Steam游戏中选择</li>
                        </ul>
                    </li>
                    <li>点击 <span class="button-demo success">保存配置</span> 应用设置</li>
                </ol>

                <h3>智能游戏进程管理</h3>
                <p>程序具备智能的游戏进程关闭功能：</p>
                <ul>
                    <li><strong>路径识别</strong>：根据游戏安装路径智能识别相关进程</li>
                    <li><strong>多种关闭方式</strong>：PowerShell、CMD、WMIC等多种关闭方法</li>
                    <li><strong>已知游戏支持</strong>：内置PUBG、CS:GO、Dota2、Rust等游戏的进程映射</li>
                    <li><strong>安全过滤</strong>：自动排除系统进程，避免误关闭</li>
                </ul>

                <div class="tip">
                    如果游戏进程无法自动关闭，程序会提供详细的错误信息和建议。
                </div>
            </div>

            <!-- 安妮程序 -->
            <div class="section" id="annie-features">
                <h2>安妮程序</h2>

                <h3>安妮程序配置</h3>
                <ol class="step-list">
                    <li>在配置界面中启用"启用安妮程序"选项</li>
                    <li>设置RAR解压密码（默认：aaa111）</li>
                    <li>确保程序目录下有 <code>an.rar</code> 文件</li>
                    <li>保存配置后，登录时会自动启动安妮程序</li>
                </ol>

                <h3>安妮程序工作流程</h3>
                <ol class="step-list">
                    <li>Steam登录成功后自动解压 <code>an.rar</code></li>
                    <li>启动 <code>an.exe</code> 程序</li>
                    <li>监控程序弹窗并自动处理</li>
                    <li>处理完成后自动清理临时文件</li>
                    <li>启动配置的游戏</li>
                </ol>

                <div class="warning">
                    安妮程序功能需要相应的RAR文件支持，请确保文件完整性。
                </div>
            </div>
