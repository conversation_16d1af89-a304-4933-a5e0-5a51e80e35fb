// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {main} from '../models';

export function AddAccount(arg1:string,arg2:string,arg3:string):Promise<void>;

export function AddAccountWithoutSteamID(arg1:string,arg2:string,arg3:string):Promise<void>;

export function CheckAccountBanStatus(arg1:number):Promise<void>;

export function ClearLogs():Promise<void>;

export function DeleteAccount(arg1:number):Promise<void>;

export function GetAccountStats():Promise<Record<string, number>>;

export function GetAccounts():Promise<Array<main.Account>>;

export function GetAccountsPaginated(arg1:number,arg2:number):Promise<main.AccountListResponse>;

export function GetConfig():Promise<main.Config>;

export function GetInstalledSteamGames():Promise<Array<main.SteamGame>>;

export function GetLogs():Promise<Array<main.LogEntry>>;

export function Greet(arg1:string):Promise<string>;

export function ImportAccounts(arg1:string):Promise<Record<string, any>>;

export function LaunchGame():Promise<void>;

export function LoginSteam(arg1:string,arg2:string):Promise<void>;

export function ParseImportLine(arg1:string):Promise<main.ImportAccount>;

export function ResolveSteamID(arg1:string):Promise<string>;

export function SetAccountSteamID(arg1:number,arg2:string):Promise<void>;

export function TriggerSteamIDCheck():Promise<Record<string, any>>;

export function UpdateAccount(arg1:number,arg2:string,arg3:string,arg4:string):Promise<void>;

export function UpdateConfig(arg1:boolean,arg2:boolean,arg3:boolean,arg4:string,arg5:string,arg6:string):Promise<void>;
