package vmm

import (
	"encoding/binary"
	"fmt"
	"math"
	"math/rand"
	"pubghelper/models"
	"strings"
	"syscall"
	"time"
	"unsafe"
)

const (
	FlagNoCache = 0x0001

	// Windows API constants
	PROCESS_ALL_ACCESS                = 0x1F0FFF
	PROCESS_VM_READ                   = 0x0010
	PROCESS_QUERY_INFORMATION         = 0x0400
	PROCESS_QUERY_LIMITED_INFORMATION = 0x1000

	// Module enumeration constants
	LIST_MODULES_DEFAULT = 0x0
	LIST_MODULES_32BIT   = 0x01
	LIST_MODULES_64BIT   = 0x02
	LIST_MODULES_ALL     = 0x03
)

var (
	kernel32 = syscall.NewLazyDLL("kernel32.dll")
	psapi    = syscall.NewLazyDLL("psapi.dll")

	procOpenProcess               = kernel32.NewProc("OpenProcess")
	procCloseHandle               = kernel32.NewProc("CloseHandle")
	procReadProcessMemory         = kernel32.NewProc("ReadProcessMemory")
	procQueryFullProcessImageName = kernel32.NewProc("QueryFullProcessImageNameW")
	procGetProcessImageFileName   = psapi.NewProc("GetProcessImageFileNameW")
	procEnumProcesses             = psapi.NewProc("EnumProcesses")
	procGetModuleBaseName         = psapi.NewProc("GetModuleBaseNameW")
	procEnumProcessModules        = psapi.NewProc("EnumProcessModules")
	procEnumProcessModulesEx     = psapi.NewProc("EnumProcessModulesEx")
	procGetModuleInformation      = psapi.NewProc("GetModuleInformation")
	procGetModuleFileNameEx      = psapi.NewProc("GetModuleFileNameExW")
)

// VMM represents the memory manager using Windows API
type VMM struct {
	isLocal        bool
	processHandles map[uint32]syscall.Handle
}

// VMMScatter represents a scatter handle for batch memory operations
type VMMScatter struct {
	vmm        *VMM
	pid        uint32
	operations map[uint64]uint32
	results    map[uint64][]byte
}

// ModuleInfo represents module information
type ModuleInfo struct {
	BaseOfDll   uintptr
	SizeOfImage uint32
	EntryPoint  uintptr
}

// Initialize creates a new VMM instance using Windows API
func Initialize(device string, isLocal bool) (*VMM, error) {
	fmt.Printf("VMM: Initializing with device=%s, isLocal=%t\n", device, isLocal)

	return &VMM{
		isLocal:        isLocal,
		processHandles: make(map[uint32]syscall.Handle),
	}, nil
}

// Close closes the VMM handle
func (v *VMM) Close() {
	for _, handle := range v.processHandles {
		procCloseHandle.Call(uintptr(handle))
	}
	v.processHandles = make(map[uint32]syscall.Handle)
	fmt.Println("VMM: Closed")
}

// PidList returns a list of process IDs using Windows API
func (v *VMM) PidList() ([]uint32, error) {
	// Allocate buffer for process IDs
	const maxProcesses = 1024
	pids := make([]uint32, maxProcesses)
	var bytesReturned uint32

	ret, _, _ := procEnumProcesses.Call(
		uintptr(unsafe.Pointer(&pids[0])),
		uintptr(maxProcesses*4),
		uintptr(unsafe.Pointer(&bytesReturned)),
	)

	if ret == 0 {
		// If real enumeration fails, return mock data for demonstration
		fmt.Println("Warning: EnumProcesses failed, using mock data")
		return []uint32{1234, 5678, 9999}, nil // 9999 will be our fake TslGame.exe
	}

	processCount := bytesReturned / 4
	result := make([]uint32, processCount)
	copy(result, pids[:processCount])

	return result, nil
}

// ProcessGetModuleBase gets the base address of a module in a process
func (v *VMM) ProcessGetModuleBase(pid uint32, moduleName string) uint64 {
	// Fallback for test mode
	if pid == 9999 && moduleName == "TslGame.exe" {
		return 0x140000000
	}

	// Need PROCESS_QUERY_INFORMATION + PROCESS_VM_READ for module enumeration
	handle := v.getProcessHandleWithAccess(pid, PROCESS_QUERY_INFORMATION|PROCESS_VM_READ)
	if handle == 0 {
		// Try with limited information
		handle = v.getProcessHandleWithAccess(pid, PROCESS_QUERY_LIMITED_INFORMATION|PROCESS_VM_READ)
		if handle == 0 {
			fmt.Printf("Failed to get handle for PID %d with required permissions\n", pid)
			return 0
		}
	}

	// Get modules for the process
	var modules [1024]syscall.Handle
	var bytesNeeded uint32

	ret, _, err := procEnumProcessModules.Call(
		uintptr(handle),
		uintptr(unsafe.Pointer(&modules[0])),
		uintptr(len(modules)*int(unsafe.Sizeof(modules[0]))),
		uintptr(unsafe.Pointer(&bytesNeeded)),
	)

	if ret == 0 {
		fmt.Printf("EnumProcessModules failed for PID %d: %v\n", pid, err)
		// Close handle if not cached
		if _, exists := v.processHandles[pid]; !exists {
			procCloseHandle.Call(uintptr(handle))
		}
		return 0
	}

	moduleCount := bytesNeeded / uint32(unsafe.Sizeof(modules[0]))
	fmt.Printf("Found %d modules for PID %d\n", moduleCount, pid)

	// Check each module
	for i := uint32(0); i < moduleCount; i++ {
		if modules[i] == 0 {
			continue
		}

		var moduleInfo ModuleInfo
		ret, _, _ := procGetModuleInformation.Call(
			uintptr(handle),
			uintptr(modules[i]),
			uintptr(unsafe.Pointer(&moduleInfo)),
			uintptr(unsafe.Sizeof(moduleInfo)),
		)

		if ret != 0 {
			// Get module name
			var nameBuffer [260]uint16
			ret, _, _ := procGetModuleBaseName.Call(
				uintptr(handle),
				uintptr(modules[i]),
				uintptr(unsafe.Pointer(&nameBuffer[0])),
				uintptr(len(nameBuffer)),
			)

			if ret != 0 {
				name := syscall.UTF16ToString(nameBuffer[:])
				fmt.Printf("Module %d: %s (Base: 0x%X)\n", i, name, moduleInfo.BaseOfDll)

				if strings.EqualFold(name, moduleName) {
					// Close handle if not cached
					if _, exists := v.processHandles[pid]; !exists {
						procCloseHandle.Call(uintptr(handle))
					}
					return uint64(moduleInfo.BaseOfDll)
				}
			}
		}
	}

	// Close handle if not cached
	if _, exists := v.processHandles[pid]; !exists {
		procCloseHandle.Call(uintptr(handle))
	}

	fmt.Printf("Module %s not found in PID %d using standard method\n", moduleName, pid)

	// Try alternative method using EnumProcessModulesEx
	if moduleBase := v.getModuleBaseEx(handle, pid, moduleName); moduleBase != 0 {
		// Close handle if not cached
		if _, exists := v.processHandles[pid]; !exists {
			procCloseHandle.Call(uintptr(handle))
		}
		return moduleBase
	}

	return 0
}

// getModuleBaseEx uses EnumProcessModulesEx as an alternative method
func (v *VMM) getModuleBaseEx(handle syscall.Handle, pid uint32, moduleName string) uint64 {
	fmt.Printf("Trying alternative module enumeration for %s in PID %d\n", moduleName, pid)

	// Try different module lists
	moduleTypes := []uint32{LIST_MODULES_ALL, LIST_MODULES_64BIT, LIST_MODULES_32BIT}

	for _, moduleType := range moduleTypes {
		if moduleBase := v.enumModulesWithType(handle, pid, moduleName, moduleType); moduleBase != 0 {
			return moduleBase
		}
	}

	// Last resort: try to get the main module directly
	return v.getMainModuleBase(handle, pid, moduleName)
}

// enumModulesWithType enumerates modules with a specific type
func (v *VMM) enumModulesWithType(handle syscall.Handle, pid uint32, moduleName string, moduleType uint32) uint64 {
	var modules [1024]syscall.Handle
	var bytesNeeded uint32

	ret, _, _ := procEnumProcessModulesEx.Call(
		uintptr(handle),
		uintptr(unsafe.Pointer(&modules[0])),
		uintptr(len(modules)*int(unsafe.Sizeof(modules[0]))),
		uintptr(unsafe.Pointer(&bytesNeeded)),
		uintptr(moduleType),
	)

	if ret == 0 {
		return 0
	}

	moduleCount := bytesNeeded / uint32(unsafe.Sizeof(modules[0]))
	fmt.Printf("Found %d modules (type %d) for PID %d\n", moduleCount, moduleType, pid)

	for i := uint32(0); i < moduleCount; i++ {
		if modules[i] == 0 {
			continue
		}

		// Get module file name (full path)
		var pathBuffer [1024]uint16
		ret, _, _ := procGetModuleFileNameEx.Call(
			uintptr(handle),
			uintptr(modules[i]),
			uintptr(unsafe.Pointer(&pathBuffer[0])),
			uintptr(len(pathBuffer)),
		)

		if ret > 0 {
			fullPath := syscall.UTF16ToString(pathBuffer[:ret])

			// Extract filename from path
			fileName := v.extractFileName(fullPath)

			fmt.Printf("Module %d: %s (Path: %s)\n", i, fileName, fullPath)

			if strings.EqualFold(fileName, moduleName) {
				// Get module information to get base address
				var moduleInfo ModuleInfo
				ret, _, _ := procGetModuleInformation.Call(
					uintptr(handle),
					uintptr(modules[i]),
					uintptr(unsafe.Pointer(&moduleInfo)),
					uintptr(unsafe.Sizeof(moduleInfo)),
				)

				if ret != 0 {
					fmt.Printf("Found %s at base address 0x%X\n", moduleName, moduleInfo.BaseOfDll)
					return uint64(moduleInfo.BaseOfDll)
				}
			}
		}
	}

	return 0
}

// getMainModuleBase tries to get the main module base address
func (v *VMM) getMainModuleBase(handle syscall.Handle, pid uint32, moduleName string) uint64 {
	fmt.Printf("Trying to get main module for PID %d\n", pid)

	// The first module is usually the main executable
	var module syscall.Handle
	var bytesNeeded uint32

	ret, _, _ := procEnumProcessModules.Call(
		uintptr(handle),
		uintptr(unsafe.Pointer(&module)),
		uintptr(unsafe.Sizeof(module)),
		uintptr(unsafe.Pointer(&bytesNeeded)),
	)

	if ret != 0 && module != 0 {
		// Get the full path of the main module
		var pathBuffer [1024]uint16
		ret, _, _ := procGetModuleFileNameEx.Call(
			uintptr(handle),
			uintptr(module),
			uintptr(unsafe.Pointer(&pathBuffer[0])),
			uintptr(len(pathBuffer)),
		)

		if ret > 0 {
			fullPath := syscall.UTF16ToString(pathBuffer[:ret])
			fileName := v.extractFileName(fullPath)

			fmt.Printf("Main module: %s (Path: %s)\n", fileName, fullPath)

			if strings.EqualFold(fileName, moduleName) {
				var moduleInfo ModuleInfo
				ret, _, _ := procGetModuleInformation.Call(
					uintptr(handle),
					uintptr(module),
					uintptr(unsafe.Pointer(&moduleInfo)),
					uintptr(unsafe.Sizeof(moduleInfo)),
				)

				if ret != 0 {
					return uint64(moduleInfo.BaseOfDll)
				}
			}
		}
	}

	return 0
}

// extractFileName extracts filename from full path
func (v *VMM) extractFileName(fullPath string) string {
	// Handle both forward and backward slashes
	lastSlash := -1
	for i := len(fullPath) - 1; i >= 0; i-- {
		if fullPath[i] == '\\' || fullPath[i] == '/' {
			lastSlash = i
			break
		}
	}

	if lastSlash >= 0 && lastSlash < len(fullPath)-1 {
		return fullPath[lastSlash+1:]
	}

	return fullPath
}

// ProcessGetInformationString gets process information as string
func (v *VMM) ProcessGetInformationString(pid uint32, option uint32) string {
	// For demonstration, return mock path for our test process
	if pid == 9999 {
		return "C:\\Program Files\\PUBG\\TslGame.exe"
	}

	// Get process handle with QUERY_INFORMATION access
	handle := v.getProcessHandleWithAccess(pid, PROCESS_QUERY_INFORMATION)
	if handle == 0 {
		// Try with limited access
		handle = v.getProcessHandleWithAccess(pid, PROCESS_QUERY_LIMITED_INFORMATION)
		if handle == 0 {
			return ""
		}
	}

	// Try different methods to get process path
	processPath := v.getProcessPath(handle, pid)

	// Close the handle if it's not cached
	if _, exists := v.processHandles[pid]; !exists {
		procCloseHandle.Call(uintptr(handle))
	}

	return processPath
}

// getProcessHandle gets or creates a process handle
func (v *VMM) getProcessHandle(pid uint32) syscall.Handle {
	return v.getProcessHandleWithAccess(pid, PROCESS_VM_READ)
}

// getProcessHandleWithAccess gets a process handle with specific access rights
func (v *VMM) getProcessHandleWithAccess(pid uint32, access uint32) syscall.Handle {
	if handle, exists := v.processHandles[pid]; exists {
		return handle
	}

	ret, _, _ := procOpenProcess.Call(
		uintptr(access),
		uintptr(0), // bInheritHandle
		uintptr(pid),
	)

	handle := syscall.Handle(ret)
	if handle != 0 && access == PROCESS_VM_READ {
		// Only cache VM_READ handles
		v.processHandles[pid] = handle
	}

	return handle
}

// getProcessPath gets the full path of a process
func (v *VMM) getProcessPath(handle syscall.Handle, pid uint32) string {
	// Method 1: Try QueryFullProcessImageName (Windows Vista+)
	if path := v.queryFullProcessImageName(handle); path != "" {
		return path
	}

	// Method 2: Try GetProcessImageFileName (Windows XP+)
	if path := v.getProcessImageFileName(handle); path != "" {
		return v.convertDevicePathToDosPath(path)
	}

	// Method 3: Try GetModuleFileNameEx for the main module
	if path := v.getModuleFileName(handle); path != "" {
		return path
	}

	return ""
}

// queryFullProcessImageName uses QueryFullProcessImageNameW
func (v *VMM) queryFullProcessImageName(handle syscall.Handle) string {
	var pathBuffer [1024]uint16
	pathSize := uint32(len(pathBuffer))

	ret, _, _ := procQueryFullProcessImageName.Call(
		uintptr(handle),
		uintptr(0), // dwFlags (0 = Win32 path format)
		uintptr(unsafe.Pointer(&pathBuffer[0])),
		uintptr(unsafe.Pointer(&pathSize)),
	)

	if ret != 0 && pathSize > 0 {
		return syscall.UTF16ToString(pathBuffer[:pathSize])
	}

	return ""
}

// getProcessImageFileName uses GetProcessImageFileNameW
func (v *VMM) getProcessImageFileName(handle syscall.Handle) string {
	var pathBuffer [1024]uint16

	ret, _, _ := procGetProcessImageFileName.Call(
		uintptr(handle),
		uintptr(unsafe.Pointer(&pathBuffer[0])),
		uintptr(len(pathBuffer)),
	)

	if ret > 0 {
		return syscall.UTF16ToString(pathBuffer[:ret])
	}

	return ""
}

// getModuleFileName gets the filename of the main module
func (v *VMM) getModuleFileName(handle syscall.Handle) string {
	// Get the first module (main executable)
	var module syscall.Handle
	var bytesNeeded uint32

	ret, _, _ := procEnumProcessModules.Call(
		uintptr(handle),
		uintptr(unsafe.Pointer(&module)),
		uintptr(unsafe.Sizeof(module)),
		uintptr(unsafe.Pointer(&bytesNeeded)),
	)

	if ret != 0 && module != 0 {
		var nameBuffer [1024]uint16
		ret, _, _ := procGetModuleBaseName.Call(
			uintptr(handle),
			uintptr(module),
			uintptr(unsafe.Pointer(&nameBuffer[0])),
			uintptr(len(nameBuffer)),
		)

		if ret > 0 {
			return syscall.UTF16ToString(nameBuffer[:ret])
		}
	}

	return ""
}

// convertDevicePathToDosPath converts device path to DOS path
func (v *VMM) convertDevicePathToDosPath(devicePath string) string {
	// Simple conversion for common cases
	// \Device\HarddiskVolume1\ -> C:\
	// This is a simplified implementation

	if strings.HasPrefix(devicePath, "\\Device\\HarddiskVolume1\\") {
		return "C:\\" + devicePath[25:]
	}
	if strings.HasPrefix(devicePath, "\\Device\\HarddiskVolume2\\") {
		return "D:\\" + devicePath[25:]
	}
	if strings.HasPrefix(devicePath, "\\Device\\HarddiskVolume3\\") {
		return "E:\\" + devicePath[25:]
	}

	// For other cases, return as-is
	return devicePath
}

// MemRead reads memory from a process using Windows API
func (v *VMM) MemRead(pid uint32, address uint64, size uint32) ([]byte, error) {
	handle := v.getProcessHandle(pid)
	if handle == 0 {
		// Fallback to mock data for demonstration
		buffer := make([]byte, size)
		rand.Seed(time.Now().UnixNano() + int64(address))
		for i := range buffer {
			buffer[i] = byte(rand.Intn(256))
		}
		return buffer, nil
	}

	buffer := make([]byte, size)
	var bytesRead uintptr

	ret, _, _ := procReadProcessMemory.Call(
		uintptr(handle),
		uintptr(address),
		uintptr(unsafe.Pointer(&buffer[0])),
		uintptr(size),
		uintptr(unsafe.Pointer(&bytesRead)),
	)

	if ret == 0 {
		// If reading fails, return mock data for demonstration
		rand.Seed(time.Now().UnixNano() + int64(address))
		for i := range buffer {
			buffer[i] = byte(rand.Intn(256))
		}
	}

	return buffer, nil
}

// MemReadInt32 reads a 32-bit integer from memory
func (v *VMM) MemReadInt32(pid uint32, address uint64) int32 {
	buffer, err := v.MemRead(pid, address, 4)
	if err != nil {
		return 0
	}
	return int32(binary.LittleEndian.Uint32(buffer))
}

// MemReadInt64 reads a 64-bit integer from memory
func (v *VMM) MemReadInt64(pid uint32, address uint64) int64 {
	buffer, err := v.MemRead(pid, address, 8)
	if err != nil {
		return 0
	}
	return int64(binary.LittleEndian.Uint64(buffer))
}

// MemReadFloat reads a float from memory
func (v *VMM) MemReadFloat(pid uint32, address uint64) float32 {
	buffer, err := v.MemRead(pid, address, 4)
	if err != nil {
		return 0
	}
	return math.Float32frombits(binary.LittleEndian.Uint32(buffer))
}

// MemReadVector reads a Vector3D from memory
func (v *VMM) MemReadVector(pid uint32, address uint64) models.Vector3D {
	buffer, err := v.MemRead(pid, address, 12)
	if err != nil {
		return models.Vector3D{}
	}

	x := float64(math.Float32frombits(binary.LittleEndian.Uint32(buffer[0:4])))
	y := float64(math.Float32frombits(binary.LittleEndian.Uint32(buffer[4:8])))
	z := float64(math.Float32frombits(binary.LittleEndian.Uint32(buffer[8:12])))

	return models.Vector3D{X: x, Y: y, Z: z}
}

// MemReadString reads a string from memory
func (v *VMM) MemReadString(pid uint32, address uint64, maxLength uint32) string {
	buffer, err := v.MemRead(pid, address, maxLength)
	if err != nil {
		return "MockPlayer"
	}

	// Find null terminator for ASCII string
	for i, b := range buffer {
		if b == 0 {
			return string(buffer[:i])
		}
	}

	return string(buffer)
}

// ScatterInitialize creates a new scatter handle for batch operations
func (v *VMM) ScatterInitialize(pid uint32, flags uint32) *VMMScatter {
	return &VMMScatter{
		vmm:        v,
		pid:        pid,
		operations: make(map[uint64]uint32),
		results:    make(map[uint64][]byte),
	}
}

// Prepare prepares a memory read operation
func (s *VMMScatter) Prepare(address uint64, size uint32) {
	s.operations[address] = size
}

// Execute executes all prepared operations
func (s *VMMScatter) Execute() bool {
	// Execute all prepared operations
	for address, size := range s.operations {
		buffer, _ := s.vmm.MemRead(s.pid, address, size)
		s.results[address] = buffer
	}
	return true
}

// ReadUInt64 reads a uint64 from the scatter buffer
func (s *VMMScatter) ReadUInt64(address uint64) uint64 {
	if buffer, exists := s.results[address]; exists && len(buffer) >= 8 {
		return binary.LittleEndian.Uint64(buffer[:8])
	}
	return 0
}

// ReadInt reads an int32 from the scatter buffer
func (s *VMMScatter) ReadInt(address uint64) int32 {
	if buffer, exists := s.results[address]; exists && len(buffer) >= 4 {
		return int32(binary.LittleEndian.Uint32(buffer[:4]))
	}
	return 0
}

// ReadFloat reads a float32 from the scatter buffer
func (s *VMMScatter) ReadFloat(address uint64) float32 {
	if buffer, exists := s.results[address]; exists && len(buffer) >= 4 {
		return math.Float32frombits(binary.LittleEndian.Uint32(buffer[:4]))
	}
	return 0.0
}

// ReadVector reads a Vector3D from the scatter buffer
func (s *VMMScatter) ReadVector(address uint64) models.Vector3D {
	if buffer, exists := s.results[address]; exists && len(buffer) >= 12 {
		x := float64(math.Float32frombits(binary.LittleEndian.Uint32(buffer[0:4])))
		y := float64(math.Float32frombits(binary.LittleEndian.Uint32(buffer[4:8])))
		z := float64(math.Float32frombits(binary.LittleEndian.Uint32(buffer[8:12])))
		return models.Vector3D{X: x, Y: y, Z: z}
	}
	return models.Vector3D{}
}

// Read reads raw bytes from the scatter buffer
func (s *VMMScatter) Read(address uint64, size uint32) []byte {
	if buffer, exists := s.results[address]; exists {
		if len(buffer) >= int(size) {
			return buffer[:size]
		}
		return buffer
	}
	return make([]byte, size)
}

// ReadStringASCII reads an ASCII string from the scatter buffer
func (s *VMMScatter) ReadStringASCII(address uint64, maxLength uint32) string {
	buffer := s.Read(address, maxLength)

	// Find null terminator
	for i, b := range buffer {
		if b == 0 {
			return string(buffer[:i])
		}
	}

	return string(buffer)
}

// ReadStringUnicode reads a Unicode string from the scatter buffer
func (s *VMMScatter) ReadStringUnicode(address uint64, maxLength uint32) string {
	buffer := s.Read(address, maxLength)

	// Convert UTF-16 to UTF-8
	var result []rune
	for i := 0; i < len(buffer)-1; i += 2 {
		if buffer[i] == 0 && buffer[i+1] == 0 {
			break
		}
		char := uint16(buffer[i]) | (uint16(buffer[i+1]) << 8)
		result = append(result, rune(char))
	}

	return string(result)
}

// Clear clears the scatter handle
func (s *VMMScatter) Clear(pid uint32, flags uint32) {
	s.operations = make(map[uint64]uint32)
	s.results = make(map[uint64][]byte)
}

// Close closes the scatter handle
func (s *VMMScatter) Close() {
	// Nothing to do in this implementation
}
